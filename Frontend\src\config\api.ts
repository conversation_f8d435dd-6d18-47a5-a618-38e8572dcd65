// API Configuration for <PERSON><PERSON> Admin Frontend
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:5000',
  ENDPOINTS: {
    // Health check
    HEALTH: '/api/health',
    
    // Authentication
    AUTH: {
      LOGIN: '/api/auth/login',
      REGISTER: '/api/auth/register',
      FORGOT_PASSWORD: '/api/auth/forgot-password',
      RESET_PASSWORD: '/api/auth/reset-password',
      VERIFY_EMAIL: '/api/auth/verify-email',
      REFRESH_TOKEN: '/api/auth/refresh-token',
    },
    
    // Admin endpoints
    ADMIN: {
      // Products
      PRODUCTS: '/api/admin/products',
      PRODUCT_BY_ID: (id: string) => `/api/admin/products/${id}`,
      PRODUCT_FEATURED: (id: string) => `/api/admin/products/${id}/featured`,
      PRODUCT_STOCK: (id: string) => `/api/admin/products/${id}/stock`,
      PRODUCT_STATS: '/api/admin/products/stats',
      
      // Categories
      CATEGORIES: '/api/admin/categories',
      CATEGORY_BY_ID: (id: string) => `/api/admin/categories/${id}`,
      CATEGORY_STATS: '/api/admin/categories/stats',
      CATEGORY_PRODUCTS: '/api/admin/categories/category-products',
      
      // Orders
      ORDERS: '/api/admin/orders',
      ORDER_BY_ID: (id: string) => `/api/admin/orders/${id}`,
      ORDER_STATUS: (id: string) => `/api/admin/orders/${id}/status`,
      ORDER_STATS: '/api/admin/orders/stats',
      
      // Users/Customers
      USERS: '/api/admin/users',
      USER_BY_ID: (id: string) => `/api/admin/users/${id}`,
      USER_STATUS: (id: string) => `/api/admin/users/${id}/status`,
      
      // Patients
      PATIENTS: '/api/admin/patients',
      PATIENT_BY_ID: (id: string) => `/api/admin/patients/${id}`,
      
      // Complaints
      COMPLAINTS: '/api/admin/complaints',
      COMPLAINT_BY_ID: (id: string) => `/api/admin/complaints/${id}`,
      COMPLAINT_STATUS: (id: string) => `/api/admin/complaints/${id}/status`,
      
      // Returns
      RETURNS: '/api/admin/returns',
      RETURN_BY_ID: (id: string) => `/api/admin/returns/${id}`,
      RETURN_STATUS: (id: string) => `/api/admin/returns/${id}/status`,
      
      // Banners
      BANNERS: '/api/admin/banners',
      BANNER_BY_ID: (id: string) => `/api/admin/banners/${id}`,
    },
    
    // User endpoints
    USER: {
      PRODUCTS: '/api/user/products',
      PRODUCT_BY_ID: (id: string) => `/api/user/products/${id}`,
      FEATURED_PRODUCTS: '/api/user/products/featured',
      
      CART: '/api/user/cart',
      CART_ADD: '/api/user/cart/add',
      CART_UPDATE: '/api/user/cart/update',
      CART_REMOVE: '/api/user/cart/remove',
      
      ORDERS: '/api/user/orders',
      ORDER_BY_ID: (id: string) => `/api/user/orders/${id}`,
      
      PROFILE: '/api/user/users/profile',
      UPDATE_PROFILE: '/api/user/users/profile',
    }
  },
  
  // Request timeout in milliseconds
  TIMEOUT: 30000,
  
  // Default headers
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
  }
};

// HTTP Methods
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
} as const;

// API Response interface
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Error types
export interface ApiError {
  message: string;
  status: number;
  code?: string;
}

// Request configuration interface
export interface RequestConfig {
  method?: keyof typeof HTTP_METHODS;
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string | number>;
  timeout?: number;
}
