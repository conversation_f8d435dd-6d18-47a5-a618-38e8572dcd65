import { apiService } from './apiService';
import { API_CONFIG } from '@/config/api';
import { ApiResponse } from '@/config/api';

// Order interfaces
export interface Order {
  _id?: string;
  id?: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  items: Array<{
    productId: string;
    productName: string;
    quantity: number;
    price: number;
    total: number;
  }>;
  subtotal: number;
  tax?: number;
  shipping?: number;
  total: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'returned';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  notes?: string;
  trackingNumber?: string;
  estimatedDelivery?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface OrderFilters {
  page?: number;
  limit?: number;
  status?: string;
  paymentStatus?: string;
  customerId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  confirmedOrders: number;
  shippedOrders: number;
  deliveredOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  topCustomers: Array<{
    customerId: string;
    customerName: string;
    orderCount: number;
    totalSpent: number;
  }>;
  recentOrders: Order[];
}

class OrderService {
  // Get all orders (admin)
  async getOrders(filters?: OrderFilters): Promise<ApiResponse<{
    orders: Order[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }>> {
    const params: Record<string, string | number> = {};
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params[key] = value;
        }
      });
    }
    
    return apiService.get(API_CONFIG.ENDPOINTS.ADMIN.ORDERS, params);
  }

  // Get order by ID
  async getOrderById(id: string): Promise<ApiResponse<{ order: Order }>> {
    return apiService.get(API_CONFIG.ENDPOINTS.ADMIN.ORDER_BY_ID(id));
  }

  // Update order status
  async updateOrderStatus(id: string, status: Order['status']): Promise<ApiResponse<{ order: Order }>> {
    return apiService.put(API_CONFIG.ENDPOINTS.ADMIN.ORDER_STATUS(id), { status });
  }

  // Update order
  async updateOrder(id: string, orderData: Partial<Order>): Promise<ApiResponse<{ order: Order }>> {
    return apiService.put(API_CONFIG.ENDPOINTS.ADMIN.ORDER_BY_ID(id), orderData);
  }

  // Delete order
  async deleteOrder(id: string): Promise<ApiResponse> {
    return apiService.delete(API_CONFIG.ENDPOINTS.ADMIN.ORDER_BY_ID(id));
  }

  // Get order statistics
  async getOrderStats(): Promise<ApiResponse<{ stats: OrderStats }>> {
    return apiService.get(API_CONFIG.ENDPOINTS.ADMIN.ORDER_STATS);
  }

  // Bulk update order status
  async bulkUpdateOrderStatus(orderIds: string[], status: Order['status']): Promise<ApiResponse> {
    return apiService.put(`${API_CONFIG.ENDPOINTS.ADMIN.ORDERS}/bulk-status`, {
      orderIds,
      status
    });
  }

  // Export orders
  async exportOrders(filters?: OrderFilters): Promise<ApiResponse<{ downloadUrl: string }>> {
    const params: Record<string, string | number> = {};
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params[key] = value;
        }
      });
    }
    
    return apiService.get(`${API_CONFIG.ENDPOINTS.ADMIN.ORDERS}/export`, params);
  }
}

// Export singleton instance
export const orderService = new OrderService();
export default orderService;
