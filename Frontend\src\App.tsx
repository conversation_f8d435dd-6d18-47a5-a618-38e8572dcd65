import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "@/components/theme-provider";
import { ProfileProvider, useProfile } from "@/contexts/ProfileContext";
import { SearchProvider } from "@/contexts/SearchContext";
import {
  BrowserRouter,
  Routes,
  Route,
  Link,
  useNavigate,
  useLocation,
} from "react-router-dom";
import {
  SidebarProvider,
  useSidebar,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import ThemeToggle from "@/components/ThemeToggle";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import IntelligentSearch from "@/components/IntelligentSearch";
import { Bell, User, LogOut, Menu, X } from "lucide-react";
import { useState, useEffect } from "react";

// Auth Components
import SignIn from "@/components/auth/SignIn";
import SignUp from "@/components/auth/SignUp";
import ForgotPassword from "@/components/auth/ForgotPassword";
import TwoFactorVerification from "@/components/auth/TwoFactorVerification";

// Main Components
import Dashboard from "@/components/Dashboard";
import Products from "@/components/Products";
import Orders from "@/components/Orders";
import Customers from "@/components/Customers";
import Inventory from "@/components/Inventory";
import Consultant from "@/components/Consultant";
import Analytics from "@/components/Analytics";
import CouponsReferral from "@/components/CouponsReferral";
import ComplaintsFeedback from "@/components/ComplaintsFeedback";
import ReviewManagement from "@/components/ReviewManagement";
import Returns from "@/components/Returns";
import StaffManagement from "@/components/StaffManagement";
import Notifications from "@/components/Notifications";
import Calendar from "@/components/Calendar";
import ProfilePage from "@/components/ProfilePage";
import ApiTest from "@/components/ApiTest";

const queryClient = new QueryClient();

// Header component with notification and user profile
const TopHeader = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [profileOpen, setProfileOpen] = useState(false);
  const [previousPath, setPreviousPath] = useState<string>("/");
  const { profileData, getInitials } = useProfile();

  const { toggleSidebar, open: sidebarOpen } = useSidebar();

  const recentActivities = [
    {
      id: 1,
      type: "order",
      message: "New order #ORD-2024-003 received",
      time: "2 min ago",
    },
    {
      id: 2,
      type: "coupon",
      message: "Coupon SAVE20 expires in 2 days",
      time: "1 hour ago",
    },
    {
      id: 3,
      type: "inventory",
      message: "Low stock alert: Ashwagandha Capsules",
      time: "3 hours ago",
    },
    {
      id: 4,
      type: "order",
      message: "Order #ORD-2024-002 shipped",
      time: "5 hours ago",
    },
  ];

  // Global keyboard handler for Ctrl+C sidebar toggle
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (
        event.key === "c" &&
        (event.ctrlKey || event.metaKey) &&
        !(event.target instanceof HTMLInputElement) &&
        !(event.target instanceof HTMLTextAreaElement) &&
        !(event.target instanceof HTMLSelectElement) &&
        !(
          event.target instanceof Element &&
          event.target.closest('[contenteditable="true"]')
        )
      ) {
        event.preventDefault();
        toggleSidebar();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [toggleSidebar]);

  const handleNotificationClick = () => {
    if (location.pathname === "/notifications") {
      // If already on notifications page, go back to previous page
      navigate(previousPath);
    } else {
      // Store current path and navigate to notifications
      setPreviousPath(location.pathname);
      navigate("/notifications");
    }
  };

  const handleSignOut = () => {
    // Clear any stored authentication data
    localStorage.removeItem("auth-token");
    // Navigate to sign in page
    navigate("/auth/signin");
  };

  return (
    <div
      className="header-container fixed top-0 right-0 z-50 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 theme-transition"
      style={{
        left: sidebarOpen ? "var(--sidebar-width)" : "0",
      }}
    >
      <div className="flex items-center justify-between px-4 sm:px-6 py-4">
        {/* Left side - Sidebar Toggle and Search */}
        <div className="flex items-center gap-2 sm:gap-4 flex-1">
          {/* Sidebar Toggle Button */}
          <button
            onClick={toggleSidebar}
            className="p-2 text-gray-600 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200 flex items-center justify-center shrink-0"
            aria-label="Toggle Sidebar"
            title={`${sidebarOpen ? "Close" : "Open"} Sidebar (Press Ctrl+C)`}
          >
            {sidebarOpen ? (
              <X className="w-5 h-5" />
            ) : (
              <Menu className="w-5 h-5" />
            )}
          </button>

          {/* Logo and Brand Name - Only show when sidebar is collapsed */}
          {!sidebarOpen && (
            <div className="hidden md:flex items-center gap-3 px-3 py-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 animate-in fade-in slide-in-from-left-2 duration-300 shrink-0 shadow-sm">
              {/* Logo */}
              <div className="relative w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
                {/* Light mode logo */}
                <img
                  src="/lovable-uploads/73a3c9d3-4dc2-424c-af2e-8e092b3e5101.png"
                  alt="Dr. Kumar Laboratories"
                  className="absolute inset-0 w-full h-full object-contain transition-opacity duration-500 ease-in-out dark:opacity-0"
                />
                {/* Dark mode logo */}
                <img
                  src="/lovable-uploads/0df92a85-211b-467d-99a6-583490e9ebba.png"
                  alt="Dr. Kumar Laboratories"
                  className="absolute inset-0 w-full h-full object-contain transition-opacity duration-500 ease-in-out opacity-0 dark:opacity-100"
                />
              </div>
              {/* Subtle separator */}
              <div className="w-px h-5 bg-gray-300 dark:bg-gray-600 transition-colors duration-500 ease-in-out"></div>
              {/* Brand Name */}
              <span className="text-sm font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out whitespace-nowrap">
                Dr. Kumar Laboratories
              </span>
            </div>
          )}

          {/* Logo Only - Show on smaller screens when sidebar is collapsed */}
          {!sidebarOpen && (
            <div className="flex md:hidden items-center justify-center w-10 h-10 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 animate-in fade-in slide-in-from-left-2 duration-300 shrink-0 shadow-sm">
              {/* Logo */}
              <div className="relative w-6 h-6 rounded-full overflow-hidden flex-shrink-0">
                {/* Light mode logo */}
                <img
                  src="/lovable-uploads/73a3c9d3-4dc2-424c-af2e-8e092b3e5101.png"
                  alt="Dr. Kumar Laboratories"
                  className="absolute inset-0 w-full h-full object-contain transition-opacity duration-500 ease-in-out dark:opacity-0"
                />
                {/* Dark mode logo */}
                <img
                  src="/lovable-uploads/0df92a85-211b-467d-99a6-583490e9ebba.png"
                  alt="Dr. Kumar Laboratories"
                  className="absolute inset-0 w-full h-full object-contain transition-opacity duration-500 ease-in-out opacity-0 dark:opacity-100"
                />
              </div>
            </div>
          )}

          {/* Intelligent Search Bar - Positioned with proper spacing */}
          <div
            className={`header-search flex-1 max-w-md transition-all duration-300 ease-in-out ${
              !sidebarOpen ? "ml-3 md:ml-4" : ""
            }`}
          >
            <IntelligentSearch placeholder="Search by product, customer, category..." />
          </div>
        </div>

        {/* Right side controls */}
        <div className="flex items-center gap-2 sm:gap-4 shrink-0">
          {/* Notification Button */}
          <button
            onClick={handleNotificationClick}
            className="relative p-2 text-gray-600 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors duration-200"
            title="Notifications"
          >
            <Bell className="w-5 h-5 sm:w-6 sm:h-6" />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center">
              {recentActivities.length}
            </span>
          </button>

          {/* Profile Dropdown */}
          <DropdownMenu open={profileOpen} onOpenChange={setProfileOpen}>
            <DropdownMenuTrigger asChild>
              <button
                className="w-8 h-8 rounded-full flex items-center justify-center text-white font-medium hover:opacity-80 transition-all duration-200 overflow-hidden border-2 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
                title="Profile Menu"
              >
                {profileData.profileImage ? (
                  <div className="w-full h-full rounded-full overflow-hidden">
                    <img
                      src={profileData.profileImage}
                      alt={profileData.name}
                      className="w-full h-full object-cover object-center"
                    />
                  </div>
                ) : (
                  <div className="w-full h-full bg-gray-400 dark:bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-500 dark:hover:bg-gray-500 transition-colors">
                    <User className="w-4 h-4 text-white" />
                  </div>
                )}
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-56 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
            >
              {/* Profile Info Section */}
              <div className="flex items-center gap-3 p-3 border-b border-gray-200 dark:border-gray-700">
                <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-gray-200 dark:border-gray-600">
                  {profileData.profileImage ? (
                    <img
                      src={profileData.profileImage}
                      alt={profileData.name}
                      className="w-full h-full object-cover object-center"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-400 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-white" />
                    </div>
                  )}
                </div>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {profileData.name}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {profileData.email}
                  </span>
                </div>
              </div>

              <DropdownMenuItem asChild>
                <Link
                  to="/profile"
                  className="flex items-center gap-2 px-4 py-2 cursor-pointer text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white"
                >
                  <User className="w-4 h-4" />
                  Profile
                </Link>
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={handleSignOut}
                className="flex items-center gap-2 px-4 py-2 cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
              >
                <LogOut className="w-4 h-4" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};

// Main App Layout Component
const AppLayout = ({ children }: { children: React.ReactNode }) => (
  <SidebarProvider>
    <div className="min-h-screen flex w-full bg-gray-50 dark:bg-[#1E1E1E] transition-colors duration-500 ease-in-out">
      <AppSidebar />
      <div className="flex-1 flex flex-col transition-colors duration-500 ease-in-out">
        <TopHeader />
        <main className="flex-1 pt-20 p-6 transition-colors duration-500 ease-in-out">
          {children}
        </main>
      </div>
    </div>
    <ThemeToggle />
  </SidebarProvider>
);

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
      <ProfileProvider>
        <TooltipProvider>
          <div className="transition-colors duration-500 ease-in-out">
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <SearchProvider>
                <Routes>
                  {/* Authentication Routes */}
                  <Route path="/auth/signin" element={<SignIn />} />
                  <Route path="/auth/signup" element={<SignUp />} />
                  <Route
                    path="/auth/forgot-password"
                    element={<ForgotPassword />}
                  />
                  <Route
                    path="/auth/two-factor"
                    element={<TwoFactorVerification />}
                  />

                  {/* Main Application Routes */}
                  <Route
                    path="/"
                    element={
                      <AppLayout>
                        <Dashboard />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/dashboard"
                    element={
                      <AppLayout>
                        <Dashboard />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/products"
                    element={
                      <AppLayout>
                        <Products />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/orders"
                    element={
                      <AppLayout>
                        <Orders />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/customers"
                    element={
                      <AppLayout>
                        <Customers />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/inventory"
                    element={
                      <AppLayout>
                        <Inventory />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/consultant"
                    element={
                      <AppLayout>
                        <Consultant />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/analytics"
                    element={
                      <AppLayout>
                        <Analytics />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/coupons"
                    element={
                      <AppLayout>
                        <CouponsReferral />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/feedback"
                    element={
                      <AppLayout>
                        <ComplaintsFeedback />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/reviews"
                    element={
                      <AppLayout>
                        <ReviewManagement />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/returns"
                    element={
                      <AppLayout>
                        <Returns />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/staff"
                    element={
                      <AppLayout>
                        <StaffManagement />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/notifications"
                    element={
                      <AppLayout>
                        <Notifications />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/calendar"
                    element={
                      <AppLayout>
                        <Calendar />
                      </AppLayout>
                    }
                  />

                  <Route
                    path="/profile"
                    element={
                      <AppLayout>
                        <ProfilePage />
                      </AppLayout>
                    }
                  />
                  <Route
                    path="/api-test"
                    element={
                      <AppLayout>
                        <ApiTest />
                      </AppLayout>
                    }
                  />
                </Routes>
              </SearchProvider>
            </BrowserRouter>
          </div>
        </TooltipProvider>
      </ProfileProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
