import { toast } from "@/hooks/use-toast";

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export const handleApiError = (error: any) => {
  console.error("API Error:", error);
  toast({
    title: "Error",
    description: error.message || "An unexpected error occurred",
    variant: "destructive",
  });
};

export const handleApiSuccess = (message: string, description?: string) => {
  toast({
    title: message,
    description: description || "Operation completed successfully",
  });
};

export const showLoadingToast = (message: string = "Processing...") => {
  return toast({
    title: message,
    description: "Please wait while we process your request",
  });
};

export const exportToCSV = (data: any[], filename: string) => {
  if (!data.length) {
    toast({
      title: "No Data",
      description: "No data available to export",
      variant: "destructive",
    });
    return;
  }

  const csvContent = [
    Object.keys(data[0]).join(","),
    ...data.map((row) =>
      Object.values(row)
        .map((value) =>
          typeof value === "string" && value.includes(",")
            ? `"${value}"`
            : value
        )
        .join(",")
    ),
  ].join("\n");

  const blob = new Blob([csvContent], { type: "text/csv" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `${filename}-${new Date().toISOString().split("T")[0]}.csv`;
  a.click();
  URL.revokeObjectURL(url);

  toast({
    title: "Export Successful",
    description: `${filename} has been exported to CSV`,
  });
};

export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
  }).format(amount);
};

export const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString("en-IN", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};
