import { API_CONFIG, HTTP_METHODS, ApiResponse, ApiError, RequestConfig } from '@/config/api';
import { toast } from '@/hooks/use-toast';

class ApiService {
  private baseURL: string;
  private defaultTimeout: number;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.defaultTimeout = API_CONFIG.TIMEOUT;
  }

  // Get auth token from localStorage
  private getAuthToken(): string | null {
    return localStorage.getItem('auth-token');
  }

  // Build headers with auth token
  private buildHeaders(customHeaders?: Record<string, string>): Record<string, string> {
    const headers = { ...API_CONFIG.DEFAULT_HEADERS, ...customHeaders };
    
    const token = this.getAuthToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }

  // Build URL with query parameters
  private buildURL(endpoint: string, params?: Record<string, string | number>): string {
    const url = new URL(endpoint, this.baseURL);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, String(value));
      });
    }
    
    return url.toString();
  }

  // Handle API errors
  private handleError(error: any, showToast: boolean = true): ApiError {
    console.error('[API Error]:', error);
    
    let apiError: ApiError;
    
    if (error.response) {
      // Server responded with error status
      apiError = {
        message: error.response.data?.message || 'Server error occurred',
        status: error.response.status,
        code: error.response.data?.code
      };
    } else if (error.request) {
      // Request was made but no response received
      apiError = {
        message: 'Network error - please check your connection',
        status: 0
      };
    } else {
      // Something else happened
      apiError = {
        message: error.message || 'An unexpected error occurred',
        status: 500
      };
    }

    // Show toast notification for errors (unless disabled)
    if (showToast) {
      toast({
        title: 'Error',
        description: apiError.message,
        variant: 'destructive',
      });
    }

    return apiError;
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    config: RequestConfig = {},
    showErrorToast: boolean = true
  ): Promise<ApiResponse<T>> {
    const {
      method = HTTP_METHODS.GET,
      headers: customHeaders,
      body,
      params,
      timeout = this.defaultTimeout
    } = config;

    try {
      const url = this.buildURL(endpoint, params);
      const headers = this.buildHeaders(customHeaders);

      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const requestOptions: RequestInit = {
        method,
        headers,
        signal: controller.signal,
      };

      // Add body for non-GET requests
      if (body && method !== HTTP_METHODS.GET) {
        if (body instanceof FormData) {
          // Remove Content-Type header for FormData (browser will set it)
          delete headers['Content-Type'];
          requestOptions.body = body;
        } else {
          requestOptions.body = JSON.stringify(body);
        }
      }

      const response = await fetch(url, requestOptions);
      clearTimeout(timeoutId);

      // Parse response
      let responseData;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      // Handle non-2xx responses
      if (!response.ok) {
        throw {
          response: {
            status: response.status,
            data: responseData
          }
        };
      }

      return responseData;
    } catch (error: any) {
      if (error.name === 'AbortError') {
        const timeoutError = {
          message: 'Request timeout - please try again',
          status: 408
        };
        
        if (showErrorToast) {
          toast({
            title: 'Timeout Error',
            description: timeoutError.message,
            variant: 'destructive',
          });
        }
        
        throw timeoutError;
      }

      throw this.handleError(error, showErrorToast);
    }
  }

  // GET request
  async get<T>(endpoint: string, params?: Record<string, string | number>, showErrorToast: boolean = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: HTTP_METHODS.GET, params }, showErrorToast);
  }

  // POST request
  async post<T>(endpoint: string, data?: any, showErrorToast: boolean = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: HTTP_METHODS.POST, body: data }, showErrorToast);
  }

  // PUT request
  async put<T>(endpoint: string, data?: any, showErrorToast: boolean = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: HTTP_METHODS.PUT, body: data }, showErrorToast);
  }

  // DELETE request
  async delete<T>(endpoint: string, showErrorToast: boolean = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: HTTP_METHODS.DELETE }, showErrorToast);
  }

  // PATCH request
  async patch<T>(endpoint: string, data?: any, showErrorToast: boolean = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: HTTP_METHODS.PATCH, body: data }, showErrorToast);
  }

  // Upload file
  async uploadFile<T>(endpoint: string, formData: FormData, showErrorToast: boolean = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: HTTP_METHODS.POST, body: formData }, showErrorToast);
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    return this.get(API_CONFIG.ENDPOINTS.HEALTH, undefined, false);
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
