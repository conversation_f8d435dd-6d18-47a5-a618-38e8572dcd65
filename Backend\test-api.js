const http = require("http");

const makeRequest = (path) => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: "localhost",
      port: 5000,
      path: path,
      method: "GET",
    };

    const req = http.request(options, (res) => {
      let data = "";
      res.on("data", (chunk) => {
        data += chunk;
      });
      res.on("end", () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(data),
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data,
          });
        }
      });
    });

    req.on("error", (err) => {
      reject(err);
    });

    req.end();
  });
};

const testAPI = async () => {
  try {
    console.log("🧪 Testing Dr. Kumar Admin API Endpoints...\n");

    // Test 1: Get all products
    console.log("1️⃣ Testing GET /api/admin/products");
    const productsResponse = await makeRequest("/api/admin/products");
    console.log(`✅ Status: ${productsResponse.status}`);
    console.log(
      `📊 Products found: ${productsResponse.data.data.products.length}`
    );
    console.log(
      `📄 Sample product: ${
        productsResponse.data.data.products[0]?.name || "None"
      }\n`
    );

    // Test 2: Get product stats
    console.log("2️⃣ Testing GET /api/admin/products/stats");
    const statsResponse = await makeRequest("/api/admin/products/stats");
    console.log(`✅ Status: ${statsResponse.status}`);
    console.log(
      `📊 Total Products: ${statsResponse.data.data.stats.totalProducts}`
    );
    console.log(
      `💰 Average Price: ₹${statsResponse.data.data.stats.averagePrice}\n`
    );

    // Test 3: Get featured products
    console.log("3️⃣ Testing GET /api/user/products/featured");
    const featuredResponse = await makeRequest("/api/user/products/featured");
    console.log(`✅ Status: ${featuredResponse.status}`);
    console.log(
      `⭐ Featured products: ${featuredResponse.data.data.products.length}\n`
    );

    // Test 4: Create a new product
    console.log("4️⃣ Testing POST /api/admin/products");
    const newProduct = {
      name: "API Test Product",
      description:
        "This product was created via API test to verify POST functionality.",
      categoryId: "686c170c1ccfe84c43c1ef96",
      category: "Energy Drink",
      price: 199,
      inStock: true,
      isFeatured: false,
      images: [
        {
          url: "/uploads/api-test-product.jpg",
          altText: "API Test Product",
        },
      ],
    };

    const createResponse = await axios.post(
      "http://localhost:5000/api/admin/products",
      newProduct
    );
    console.log(`✅ Status: ${createResponse.status}`);
    console.log(`🆕 Created product: ${createResponse.data.data.product.name}`);
    console.log(`🆔 Product ID: ${createResponse.data.data.product._id}\n`);

    // Test 5: Get updated product count
    console.log("5️⃣ Testing updated product count");
    const updatedProductsResponse = await axios.get(
      "http://localhost:5000/api/admin/products"
    );
    console.log(`✅ Status: ${updatedProductsResponse.status}`);
    console.log(
      `📊 Updated product count: ${updatedProductsResponse.data.data.products.length}\n`
    );

    console.log("🎉 All API tests completed successfully!");
  } catch (error) {
    console.error("❌ API Test Error:", error.response?.data || error.message);
  }
};

testAPI();
