import { useState, useMemo, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Download, RefreshCw, Edit, Trash2, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";
import ProductModal from "./ProductModal";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";
import {
  productService,
  Product,
  ProductFilters,
} from "@/services/productService";
import { handleApiError, handleApiSuccess } from "@/utils/apiUtils";

const Products = () => {
  const { toast } = useToast();

  // State management
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [productStats, setProductStats] = useState({
    totalProducts: 0,
    activeProducts: 0,
    inactiveProducts: 0,
    totalValue: 0,
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const itemsPerPage = 10;

  // Fetch products from API
  const fetchProducts = async (filters?: ProductFilters) => {
    try {
      setIsLoading(true);
      const response = await productService.getProducts({
        page: currentPage,
        limit: itemsPerPage,
        search: searchTerm || undefined,
        inStock:
          statusFilter === "active"
            ? true
            : statusFilter === "inactive"
            ? false
            : undefined,
        category: categoryFilter !== "all" ? categoryFilter : undefined,
        ...filters,
      });

      if (response.success && response.data) {
        setProducts(response.data.products || []);
        setTotalPages(response.data.pagination?.pages || 1);
        setTotalItems(response.data.pagination?.total || 0);
      }
    } catch (error) {
      handleApiError(error);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch product statistics
  const fetchProductStats = async () => {
    try {
      const response = await productService.getProductStats();
      if (response.success && response.data?.stats) {
        const stats = response.data.stats;
        setProductStats({
          totalProducts: stats.totalProducts,
          activeProducts: stats.activeProducts,
          inactiveProducts: stats.inactiveProducts,
          totalValue: stats.averagePrice * stats.totalProducts, // Approximate total value
        });
      }
    } catch (error) {
      console.error("Failed to fetch product stats:", error);
    }
  };

  // Load data on component mount and when filters change
  useEffect(() => {
    fetchProducts();
    fetchProductStats();
  }, [currentPage, searchTerm, statusFilter, categoryFilter]);

  // Get unique categories for filter
  const categories = useMemo(() => {
    const uniqueCategories = [
      ...new Set(products.map((p) => p.category).filter(Boolean)),
    ];
    return uniqueCategories;
  }, [products]);

  // Handle pagination
  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  // Calculate dynamic stats for display
  const statsDisplay = useMemo(() => {
    return [
      {
        title: "Total Products",
        value: productStats.totalProducts.toString(),
        color: "text-gray-900",
      },
      {
        title: "Active",
        value: productStats.activeProducts.toString(),
        color: "text-green-600",
      },
      {
        title: "Inactive",
        value: productStats.inactiveProducts.toString(),
        color: "text-red-600",
      },
      {
        title: "Total Value",
        value: `₹${productStats.totalValue.toLocaleString()}`,
        color: "text-blue-600",
      },
    ];
  }, [productStats]);

  const handleAddProduct = () => {
    setSelectedProduct(null);
    setIsProductModalOpen(true);
  };

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product);
    setIsProductModalOpen(true);
  };

  const handleDeleteProduct = async (productId: string) => {
    try {
      const response = await productService.deleteProduct(productId);
      if (response.success) {
        handleApiSuccess(
          "Product Deleted",
          "Product has been successfully deleted"
        );
        fetchProducts(); // Refresh the list
        fetchProductStats(); // Refresh stats
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const handleStatusToggle = async (
    productId: string,
    currentInStock: boolean
  ) => {
    try {
      const response = await productService.toggleStockStatus(productId);
      if (response.success) {
        const newStatus = !currentInStock ? "in stock" : "out of stock";
        handleApiSuccess("Status Updated", `Product marked as ${newStatus}`);
        fetchProducts(); // Refresh the list
        fetchProductStats(); // Refresh stats
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const getStatusBadgeColor = (inStock: boolean) => {
    return inStock
      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
  };

  const handleSaveProduct = async (productData: any) => {
    try {
      if (selectedProduct) {
        // Update existing product
        const response = await productService.updateProduct(
          selectedProduct._id || selectedProduct.id!,
          productData
        );
        if (response.success) {
          handleApiSuccess(
            "Product Updated",
            "Product has been successfully updated"
          );
          fetchProducts(); // Refresh the list
          fetchProductStats(); // Refresh stats
          setIsProductModalOpen(false);
        }
      } else {
        // Create new product
        const response = await productService.createProduct(productData);
        if (response.success) {
          handleApiSuccess(
            "Product Added",
            "New product has been successfully added"
          );
          fetchProducts(); // Refresh the list
          fetchProductStats(); // Refresh stats
          setIsProductModalOpen(false);
        }
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await fetchProducts();
      await fetchProductStats();
      handleApiSuccess(
        "Products Refreshed",
        "Product data has been successfully updated"
      );
    } catch (error) {
      handleApiError(error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleExportToExcel = async () => {
    setIsExporting(true);

    try {
      // Create CSV content (which can be opened in Excel)
      const headers = [
        "Product Name",
        "Category",
        "Price",
        "In Stock",
        "Featured",
        "Last Updated",
      ];
      const csvContent = [
        headers.join(","),
        ...products.map((product) =>
          [
            `"${product.name}"`,
            product.category || "N/A",
            product.price,
            product.inStock ? "Yes" : "No",
            product.isFeatured ? "Yes" : "No",
            product.updatedAt
              ? new Date(product.updatedAt).toLocaleDateString()
              : "N/A",
          ].join(",")
        ),
      ].join("\n");

      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `products-export-${
        new Date().toISOString().split("T")[0]
      }.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      handleApiSuccess(
        "Export Successful",
        "Products have been exported to Excel format successfully"
      );
    } catch (error) {
      handleApiError(error);
    } finally {
      setIsExporting(false);
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setCategoryFilter("all");
  };

  return (
    <div className="space-y-4 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Product Management
          </h1>
          <p className="text-lg transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
            Manage your product catalog and inventory
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            className="bg-green-600 hover:bg-green-700 text-white text-base px-6 py-3"
            onClick={handleAddProduct}
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Product
          </Button>
          <Button
            variant="outline"
            onClick={handleExportToExcel}
            disabled={isExporting}
            className="text-base px-6 py-3"
          >
            <Download className="w-5 h-5 mr-2" />
            {isExporting ? "Exporting..." : "Export"}
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="text-base px-6 py-3"
          >
            <RefreshCw
              className={`w-5 h-5 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
            />
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
      </div>

      {/* Product Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsDisplay.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                  {stat.title}
                </p>
                <AnimatedNumber value={stat.value} className="text-3xl" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enhanced Search and Filters */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <span className="font-medium text-xl">
                  🔍 Search & Filter Products
                </span>
              </div>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="text-base"
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Products
                </label>
                <Input
                  placeholder="Search by name or SKU..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
                />
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <Select
                  value={categoryFilter}
                  onValueChange={setCategoryFilter}
                >
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-base text-gray-500 dark:text-gray-400">
              Showing {filteredProducts.length} of {products.length} products
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="transition-colors duration-500 ease-in-out text-gray-900 dark:text-white text-2xl">
            Products
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-8 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-lg border-b border-gray-200 dark:border-gray-600">
              <div className="text-center">Image</div>
              <div className="text-center">Product</div>
              <div className="text-center">Category</div>
              <div className="text-center">Price</div>
              <div className="text-center">Stock</div>
              <div className="text-center">Status</div>
              <div className="text-center">Last Updated</div>
              <div className="text-center">Actions</div>
            </div>

            {/* Table Rows */}
            <div className="space-y-3">
              {paginatedProducts.map((product, index) => (
                <div
                  key={index}
                  className="grid grid-cols-8 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 items-center transition-colors duration-500 ease-in-out"
                >
                  <div className="flex justify-center">
                    {product.image ? (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-12 h-12 object-cover rounded-lg border border-gray-200 dark:border-gray-600"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center border border-gray-200 dark:border-gray-600">
                        <span className="text-gray-400 text-xs">No Image</span>
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col items-center text-center">
                    <AnimatedText className="font-medium text-lg">
                      {product.name}
                    </AnimatedText>
                    <span className="text-base transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                      SKU: {product.sku}
                    </span>
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-lg text-center">
                    {product.category}
                  </div>
                  <div className="text-center">
                    <div className="flex flex-col items-center">
                      <span className="font-medium text-lg">
                        ₹{product.sellingPrice}
                      </span>
                      <span className="text-base text-gray-500 line-through">
                        ₹{product.mrp}
                      </span>
                    </div>
                  </div>
                  <div className="text-center">
                    <span
                      className={`font-medium text-lg ${
                        product.stock > 0 ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {product.stock}
                    </span>
                  </div>
                  <div className="flex justify-center">
                    <Button
                      variant="outline"
                      onClick={() =>
                        handleStatusToggle(product.id, product.status)
                      }
                      className="w-full max-w-[140px] text-base bg-red-50 hover:bg-red-100 border-red-200 text-red-700 dark:bg-black dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white"
                    >
                      <Badge
                        className={`${getStatusBadgeColor(
                          product.status
                        )} text-base`}
                      >
                        {product.status}
                      </Badge>
                    </Button>
                  </div>
                  <div className="text-center">
                    <span className="text-gray-500 dark:text-gray-400 text-base">
                      {product.updatedAt}
                    </span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditProduct(product)}
                      className="p-2"
                    >
                      <Edit className="w-5 h-5" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteProduct(product.id)}
                      className="p-2"
                    >
                      <Trash2 className="w-5 h-5" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
              totalItems={totalItems}
              itemsPerPage={10}
            />
          </div>
        </CardContent>
      </Card>

      <ProductModal
        isOpen={isProductModalOpen}
        onClose={() => setIsProductModalOpen(false)}
        product={selectedProduct}
        onSave={handleSaveProduct}
      />
    </div>
  );
};

export default Products;
