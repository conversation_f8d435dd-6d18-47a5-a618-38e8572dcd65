const express = require("express");
const dotenv = require("dotenv");
dotenv.config();
const cors = require("cors");

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// CORS configuration
app.use(
  cors({
    origin: process.env.CLIENT_URL || "http://localhost:5173",
    credentials: true,
  })
);

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({
    success: true,
    message: "Dr. <PERSON>end is running",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
  });
});

// Simple test route
app.get("/api/test", (req, res) => {
  res.json({
    success: true,
    message: "Test route working",
    data: {
      products: [
        {
          _id: "1",
          name: "Test Product",
          price: 100,
          inStock: true,
          category: "Test Category",
        },
      ],
    },
  });
});

// Mock products data
const mockProducts = [
  {
    _id: "1",
    name: "Power Stride Juice",
    categoryId: "cat1",
    category: "Health Drinks",
    description: "Pure Aloe Vera juice for digestive health",
    price: 249,
    inStock: true,
    isFeatured: true,
    images: [{ url: "/api/placeholder/60/60", altText: "Power Stride Juice" }],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "2",
    name: "Ashwagandha Capsules",
    categoryId: "cat2",
    category: "Supplements",
    description: "Premium Ashwagandha for stress relief",
    price: 599,
    inStock: true,
    isFeatured: false,
    images: [
      { url: "/api/placeholder/60/60", altText: "Ashwagandha Capsules" },
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "3",
    name: "Triphala Churna",
    categoryId: "cat3",
    category: "Ayurvedic",
    description: "Traditional Triphala powder",
    price: 149,
    inStock: false,
    isFeatured: false,
    images: [{ url: "/api/placeholder/60/60", altText: "Triphala Churna" }],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Mock categories data
const mockCategories = [
  { _id: "cat1", name: "Health Drinks", active: true },
  { _id: "cat2", name: "Supplements", active: true },
  { _id: "cat3", name: "Ayurvedic", active: true },
];

// Products API endpoints
app.get("/api/admin/products", (req, res) => {
  const { page = 1, limit = 10, search, inStock, category } = req.query;

  let filteredProducts = [...mockProducts];

  // Apply filters
  if (search) {
    filteredProducts = filteredProducts.filter(
      (p) =>
        p.name.toLowerCase().includes(search.toLowerCase()) ||
        p.description.toLowerCase().includes(search.toLowerCase())
    );
  }

  if (inStock !== undefined) {
    filteredProducts = filteredProducts.filter(
      (p) => p.inStock === (inStock === "true")
    );
  }

  if (category && category !== "all") {
    filteredProducts = filteredProducts.filter((p) => p.category === category);
  }

  // Pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: {
      products: paginatedProducts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: filteredProducts.length,
        pages: Math.ceil(filteredProducts.length / limit),
      },
    },
  });
});

// Get product by ID
app.get("/api/admin/products/:id", (req, res) => {
  const product = mockProducts.find((p) => p._id === req.params.id);
  if (!product) {
    return res.status(404).json({
      success: false,
      message: "Product not found",
    });
  }

  res.json({
    success: true,
    data: { product },
  });
});

// Create product
app.post("/api/admin/products", (req, res) => {
  const newProduct = {
    _id: Date.now().toString(),
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  mockProducts.push(newProduct);

  res.status(201).json({
    success: true,
    message: "Product created successfully",
    data: { product: newProduct },
  });
});

// Update product
app.put("/api/admin/products/:id", (req, res) => {
  const productIndex = mockProducts.findIndex((p) => p._id === req.params.id);
  if (productIndex === -1) {
    return res.status(404).json({
      success: false,
      message: "Product not found",
    });
  }

  mockProducts[productIndex] = {
    ...mockProducts[productIndex],
    ...req.body,
    updatedAt: new Date().toISOString(),
  };

  res.json({
    success: true,
    message: "Product updated successfully",
    data: { product: mockProducts[productIndex] },
  });
});

// Delete product
app.delete("/api/admin/products/:id", (req, res) => {
  const productIndex = mockProducts.findIndex((p) => p._id === req.params.id);
  if (productIndex === -1) {
    return res.status(404).json({
      success: false,
      message: "Product not found",
    });
  }

  mockProducts.splice(productIndex, 1);

  res.json({
    success: true,
    message: "Product deleted successfully",
  });
});

// Toggle stock status
app.put("/api/admin/products/:id/stock", (req, res) => {
  const productIndex = mockProducts.findIndex((p) => p._id === req.params.id);
  if (productIndex === -1) {
    return res.status(404).json({
      success: false,
      message: "Product not found",
    });
  }

  mockProducts[productIndex].inStock = !mockProducts[productIndex].inStock;
  mockProducts[productIndex].updatedAt = new Date().toISOString();

  res.json({
    success: true,
    message: "Stock status updated successfully",
    data: { product: mockProducts[productIndex] },
  });
});

// Get product stats
app.get("/api/admin/products/stats", (req, res) => {
  const stats = {
    totalProducts: mockProducts.length,
    activeProducts: mockProducts.filter((p) => p.inStock).length,
    inactiveProducts: mockProducts.filter((p) => !p.inStock).length,
    averagePrice:
      mockProducts.reduce((sum, p) => sum + p.price, 0) / mockProducts.length,
  };

  res.json({
    success: true,
    data: { stats },
  });
});

// Categories API endpoints
app.get("/api/admin/categories", (req, res) => {
  res.json({
    success: true,
    data: { categories: mockCategories },
  });
});

// Start server
app.listen(port, () => {
  console.log("🚀 Test Server Started");
  console.log(`📍 Server running on: http://localhost:${port}`);
  console.log(`📊 Health check: http://localhost:${port}/api/health`);
  console.log(`🧪 Test route: http://localhost:${port}/api/test`);
});
