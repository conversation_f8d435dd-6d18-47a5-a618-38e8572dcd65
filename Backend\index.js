const express = require("express");
const dotenv = require("dotenv");
dotenv.config();
const morgan = require("morgan");
const connectDB = require("./config/db");
const cors = require("cors");
const app = express();

const port = process.env.PORT || 5000;

// Middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// CORS configuration
app.use(
  cors({
    origin: process.env.CLIENT_URL || "http://localhost:5173",
    credentials: true,
  })
);

// Logging middleware
app.use(morgan("combined"));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log("Request Body:", JSON.stringify(req.body, null, 2));
  }
  next();
});

// Multer setup for file uploads
// Run: npm install multer
const multer = require("multer");
const upload = multer({ dest: "uploads/" }); // Default: saves files to uploads/ directory

// Admin routes - temporarily commented out for testing
// app.use("/api/admin/banners", require("./routes/admin/banners"));
// app.use("/api/admin/categories", require("./routes/admin/categories"));
// app.use("/api/admin/complaints", require("./routes/admin/complaints"));
// app.use("/api/admin/orders", require("./routes/admin/orders"));
// app.use("/api/admin/patients", require("./routes/admin/patients"));
// app.use("/api/admin/products", require("./routes/admin/products"));
// app.use("/api/admin/returns", require("./routes/admin/returns"));
// app.use("/api/admin/users", require("./routes/admin/users"));

// User routes - temporarily commented out for testing
// app.use("/api/user/cart", require("./routes/user/cart"));
// app.use("/api/user/orders", require("./routes/user/orders"));
// app.use("/api/user/products", require("./routes/user/products"));
// app.use("/api/user/users", require("./routes/user/users"));

// Static file serving for uploads
app.use("/uploads", express.static("uploads"));

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({
    success: true,
    message: "Dr. Kumar Admin Backend is running",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
  });
});

// 404 handler for undefined routes
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`,
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error(`[ERROR] ${new Date().toISOString()}:`, err.stack);
  res.status(err.statusCode || 500).json({
    success: false,
    message: err.message || "Internal Server Error",
    error: process.env.NODE_ENV === "development" ? err.stack : undefined,
  });
});

const startServer = async () => {
  try {
    // Connect to database
    await connectDB();
    console.log("✅ Database connected successfully");

    // Start server
    app.listen(port, () => {
      console.log("🚀 Dr. Kumar Admin Backend Server Started");
      console.log(`📍 Server running on: http://localhost:${port}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || "development"}`);
      console.log(`📊 Health check: http://localhost:${port}/api/health`);
      console.log("📝 Server logs will appear below...\n");
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
};

startServer();
