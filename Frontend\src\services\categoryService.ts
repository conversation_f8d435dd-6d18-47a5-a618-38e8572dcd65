import { apiService } from './apiService';
import { API_CONFIG } from '@/config/api';
import { ApiResponse } from '@/config/api';

// Category interfaces
export interface Category {
  _id?: string;
  id?: string;
  name: string;
  description?: string;
  image?: string;
  active?: boolean;
  productCount?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CategoryStats {
  totalCategories: number;
  activeCategories: number;
  inactiveCategories: number;
  categoriesWithProducts: number;
  averageProductsPerCategory: number;
  topCategories: Array<{
    _id: string;
    name: string;
    productCount: number;
  }>;
}

class CategoryService {
  // Get all categories
  async getCategories(): Promise<ApiResponse<{ categories: Category[] }>> {
    return apiService.get(API_CONFIG.ENDPOINTS.ADMIN.CATEGORIES);
  }

  // Get category by ID
  async getCategoryById(id: string): Promise<ApiResponse<{ category: Category }>> {
    return apiService.get(API_CONFIG.ENDPOINTS.ADMIN.CATEGORY_BY_ID(id));
  }

  // Create new category
  async createCategory(categoryData: Omit<Category, '_id' | 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<{ category: Category }>> {
    return apiService.post(API_CONFIG.ENDPOINTS.ADMIN.CATEGORIES, categoryData);
  }

  // Update category
  async updateCategory(id: string, categoryData: Partial<Category>): Promise<ApiResponse<{ category: Category }>> {
    return apiService.put(API_CONFIG.ENDPOINTS.ADMIN.CATEGORY_BY_ID(id), categoryData);
  }

  // Delete category
  async deleteCategory(id: string): Promise<ApiResponse> {
    return apiService.delete(API_CONFIG.ENDPOINTS.ADMIN.CATEGORY_BY_ID(id));
  }

  // Get categories with product count
  async getCategoriesWithProductCount(): Promise<ApiResponse<{ categories: Category[] }>> {
    return apiService.get(`${API_CONFIG.ENDPOINTS.ADMIN.CATEGORIES}/productcount`);
  }

  // Get products by category
  async getProductsByCategory(categoryId: string): Promise<ApiResponse<{ products: any[] }>> {
    return apiService.get(API_CONFIG.ENDPOINTS.ADMIN.CATEGORY_PRODUCTS, { categoryId });
  }

  // Get category statistics
  async getCategoryStats(): Promise<ApiResponse<{ stats: CategoryStats }>> {
    return apiService.get(API_CONFIG.ENDPOINTS.ADMIN.CATEGORY_STATS);
  }

  // Bulk delete categories
  async bulkDeleteCategories(categoryIds: string[]): Promise<ApiResponse> {
    return apiService.delete(`${API_CONFIG.ENDPOINTS.ADMIN.CATEGORIES}/bulk-delete`, {
      categoryIds
    });
  }
}

// Export singleton instance
export const categoryService = new CategoryService();
export default categoryService;
