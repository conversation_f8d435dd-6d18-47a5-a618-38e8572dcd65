import { apiService } from './apiService';
import { API_CONFIG } from '@/config/api';
import { ApiResponse } from '@/config/api';

// Product interfaces
export interface Product {
  _id?: string;
  id?: string;
  name: string;
  description?: string;
  categoryId: string;
  price: number;
  category?: string;
  inStock: boolean;
  images?: Array<{
    url: string;
    altText?: string;
  }>;
  isFeatured: boolean;
  active?: boolean;
  reviews?: Array<{
    _id: string;
    user: string;
    rating: number;
    comment: string;
  }>;
  totalReviews?: number;
  averageRating?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface ProductFilters {
  page?: number;
  limit?: number;
  category?: string;
  categoryId?: string;
  minPrice?: number;
  maxPrice?: number;
  featured?: boolean;
  inStock?: boolean;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ProductStats {
  totalProducts: number;
  activeProducts: number;
  inactiveProducts: number;
  inStockProducts: number;
  outOfStockProducts: number;
  featuredProducts: number;
  averagePrice: number;
  topRatedProducts: Product[];
  productsByCategory: Array<{
    _id: string;
    count: number;
  }>;
}

class ProductService {
  // Get all products (admin)
  async getProducts(filters?: ProductFilters): Promise<ApiResponse<{
    products: Product[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }>> {
    const params: Record<string, string | number> = {};
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params[key] = value;
        }
      });
    }
    
    return apiService.get(API_CONFIG.ENDPOINTS.ADMIN.PRODUCTS, params);
  }

  // Get product by ID
  async getProductById(id: string): Promise<ApiResponse<{ product: Product }>> {
    return apiService.get(API_CONFIG.ENDPOINTS.ADMIN.PRODUCT_BY_ID(id));
  }

  // Create new product
  async createProduct(productData: Omit<Product, '_id' | 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<{ product: Product }>> {
    return apiService.post(API_CONFIG.ENDPOINTS.ADMIN.PRODUCTS, productData);
  }

  // Update product
  async updateProduct(id: string, productData: Partial<Product>): Promise<ApiResponse<{ product: Product }>> {
    return apiService.put(API_CONFIG.ENDPOINTS.ADMIN.PRODUCT_BY_ID(id), productData);
  }

  // Delete product
  async deleteProduct(id: string): Promise<ApiResponse> {
    return apiService.delete(API_CONFIG.ENDPOINTS.ADMIN.PRODUCT_BY_ID(id));
  }

  // Toggle featured status
  async toggleFeaturedStatus(id: string): Promise<ApiResponse<{ product: Partial<Product> }>> {
    return apiService.put(API_CONFIG.ENDPOINTS.ADMIN.PRODUCT_FEATURED(id));
  }

  // Toggle stock status
  async toggleStockStatus(id: string): Promise<ApiResponse<{ product: Partial<Product> }>> {
    return apiService.put(API_CONFIG.ENDPOINTS.ADMIN.PRODUCT_STOCK(id));
  }

  // Get product statistics
  async getProductStats(): Promise<ApiResponse<{ stats: ProductStats }>> {
    return apiService.get(API_CONFIG.ENDPOINTS.ADMIN.PRODUCT_STATS);
  }

  // Upload product images
  async uploadProductImages(id: string, images: FileList): Promise<ApiResponse<{ product: Product }>> {
    const formData = new FormData();
    
    Array.from(images).forEach((file, index) => {
      formData.append('images', file);
    });

    return apiService.uploadFile(API_CONFIG.ENDPOINTS.ADMIN.PRODUCT_BY_ID(id), formData);
  }

  // Create product with images
  async createProductWithImages(
    productData: Omit<Product, '_id' | 'id' | 'createdAt' | 'updatedAt'>,
    images?: FileList
  ): Promise<ApiResponse<{ product: Product }>> {
    const formData = new FormData();
    
    // Add product data
    Object.entries(productData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, String(value));
        }
      }
    });

    // Add images if provided
    if (images && images.length > 0) {
      Array.from(images).forEach((file) => {
        formData.append('images', file);
      });
    }

    return apiService.uploadFile(API_CONFIG.ENDPOINTS.ADMIN.PRODUCTS, formData);
  }

  // Update product with images
  async updateProductWithImages(
    id: string,
    productData: Partial<Product>,
    images?: FileList
  ): Promise<ApiResponse<{ product: Product }>> {
    const formData = new FormData();
    
    // Add product data
    Object.entries(productData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, String(value));
        }
      }
    });

    // Add images if provided
    if (images && images.length > 0) {
      Array.from(images).forEach((file) => {
        formData.append('images', file);
      });
    }

    return apiService.uploadFile(API_CONFIG.ENDPOINTS.ADMIN.PRODUCT_BY_ID(id), formData);
  }

  // Get featured products (public)
  async getFeaturedProducts(limit?: number): Promise<ApiResponse<{ products: Product[] }>> {
    const params = limit ? { limit } : undefined;
    return apiService.get(API_CONFIG.ENDPOINTS.USER.FEATURED_PRODUCTS, params);
  }
}

// Export singleton instance
export const productService = new ProductService();
export default productService;
