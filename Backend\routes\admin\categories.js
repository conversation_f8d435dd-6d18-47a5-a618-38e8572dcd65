const express = require("express");
const router = express.Router();
const {
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryStats,
  bulkDeleteCategories,
  getCategories,
  getCategoriesWithProductCount,
  getProductsByCategory,
  getCategoryById,
} = require("../../controllers/categoryController");
const { protect, authorize } = require("../../middleware/auth");

// All routes are protected and require admin access
router.use(protect);
router.use(authorize("admin"));

// @route   GET /api/admin/categories/productcount
// @desc    Get categories with product count (admin)
// @access  Private/Admin
router.get(
  "/productcount",
  require("../../controllers/categoryController").getCategoriesWithProductCount
);

// @route   GET /api/admin/categories/category-products
// @desc    Get products by category (admin)
// @access  Private/Admin
router.get(
  "/category-products",
  require("../../controllers/categoryController").getProductsByCategory
);

// @route   GET /api/admin/categories/stats
// @desc    Get category statistics (admin)
// @access  Private/Admin
router.get(
  "/stats",
  require("../../controllers/categoryController").getCategoryStats
);

// @route   DELETE /api/admin/categories/bulk-delete
// @desc    Bulk delete categories (admin)
// @access  Private/Admin
router.delete(
  "/bulk-delete",
  require("../../controllers/categoryController").bulkDeleteCategories
);

// @route   GET /api/admin/categories
// @desc    Get all categories (admin)
// @access  Private/Admin
router.get("/", require("../../controllers/categoryController").getCategories);

// @route   POST /api/admin/categories
// @desc    Create category (admin)
// @access  Private/Admin
router.post(
  "/",
  require("../../controllers/categoryController").createCategory
);

// @route   GET /api/admin/categories/:id
// @desc    Get category by ID (admin)
// @access  Private/Admin
router.get(
  "/:id",
  require("../../controllers/categoryController").getCategoryById
);

// @route   PUT /api/admin/categories/:id
// @desc    Update category (admin)
// @access  Private/Admin
router.put(
  "/:id",
  require("../../controllers/categoryController").updateCategory
);

// @route   DELETE /api/admin/categories/:id
// @desc    Delete category (admin)
// @access  Private/Admin
router.delete(
  "/:id",
  require("../../controllers/categoryController").deleteCategory
);

module.exports = router;
