import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const ApiTest = () => {
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [testData, setTestData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testHealthEndpoint = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/health');
      const data = await response.json();
      setHealthStatus(data);
    } catch (error) {
      setHealthStatus({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testDataEndpoint = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/test');
      const data = await response.json();
      setTestData(data);
    } catch (error) {
      setTestData({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>API Connection Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={testHealthEndpoint} disabled={loading}>
              Test Health Endpoint
            </Button>
            <Button onClick={testDataEndpoint} disabled={loading}>
              Test Data Endpoint
            </Button>
          </div>

          {healthStatus && (
            <div className="p-4 bg-gray-100 rounded">
              <h3 className="font-semibold">Health Status:</h3>
              <pre>{JSON.stringify(healthStatus, null, 2)}</pre>
            </div>
          )}

          {testData && (
            <div className="p-4 bg-gray-100 rounded">
              <h3 className="font-semibold">Test Data:</h3>
              <pre>{JSON.stringify(testData, null, 2)}</pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiTest;
